'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button, SuccessModal, OTPInput } from '@/shared/components/ui';
import { ApiResponse } from '@/types/api';
import { authApiService } from '../services/auth-api';

interface VerifyEmailProps {
  onSuccess?: () => void;
}

export function VerifyEmail({ onSuccess }: VerifyEmailProps) {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';

  const [code, setCode] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [error, setError] = useState<string>('');

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleOTPComplete = (otpValue: string) => {
    setCode(otpValue);
    setError(''); // Clear error when user types
  };

  const handleVerify = async () => {
    if (code.length !== 6) return;

    setIsVerifying(true);
    setError('');

    try {
      await authApiService.verifyEmailOTP({
        email,
        otp: code,
      });

      // Success
      setShowSuccessModal(true);

    } catch (error) {
      console.error('Verification error:', error);
      if (error instanceof Error) {
        setError(error.message || 'Verification failed');
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResend = async () => {
    if (resendCooldown > 0 || !email) return;

    setIsResending(true);
    setError('');

    try {
      await authApiService.resendVerificationOTP(email);

      // Success - start cooldown and clear current code
      setResendCooldown(60);
      setCode('');

    } catch (error) {
      console.error('Resend error:', error);
      if (error instanceof Error) {
        setError(error.message || 'Failed to resend code');
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setIsResending(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onSuccess?.();
  };

  const isCodeComplete = code.length === 6;

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Enter Verification Code
          </h3>
          {email && (
            <p className="text-sm text-gray-600">
              We sent a code to <span className="font-medium">{email}</span>
            </p>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm text-center">
            {error}
          </div>
        )}

        {/* OTP Input */}
        <div className="flex justify-center">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setCode}
            className="gap-3"
          />
        </div>

        {/* Verify Button */}
        <Button
          onClick={handleVerify}
          disabled={!isCodeComplete || isVerifying}
          className="w-full"
          size="lg"
        >
          {isVerifying ? 'Verifying...' : 'Verify'}
        </Button>

        {/* Resend Link */}
        <div className="text-center">
          <span className="text-gray-600">Haven't received OTP? </span>
          <button
            onClick={handleResend}
            disabled={resendCooldown > 0 || isResending || !email}
            className="text-amber-800 hover:text-amber-900 font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {isResending ? 'Sending...' : resendCooldown > 0 ? `Resend (${resendCooldown}s)` : 'Resend'}
          </button>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title="Email Verified!"
        buttonText="Continue"
        onButtonClick={handleSuccessModalClose}
      />
    </>
  );
}
