import React from 'react';
import type { UserRole } from '@/types/auth';
import { UserIcon, BarberIcon, ShopIcon } from '@/shared/components/icons';

export interface SignupTab {
  id: UserRole;
  label: string;
  icon: React.ReactNode;
}

export const SIGNUP_TABS: SignupTab[] = [
  {
    id: 'client' as const,
    label: 'Client',
    icon: <UserIcon size="lg" />,
  },
  {
    id: 'barber' as const,
    label: 'Barber',
    icon: <BarberIcon size="lg" />,
  },
  {
    id: 'shop_owner' as const,
    label: 'Shop Owner',
    icon: <ShopIcon size="lg" />,
  },
];
