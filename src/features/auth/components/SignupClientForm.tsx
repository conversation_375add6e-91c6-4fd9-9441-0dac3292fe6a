'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input } from '@/shared/components/ui';
import { useFormValidation, createEmailValidation, createPasswordValidation, createRequiredValidation } from '@/shared/hooks';
import {
  ClientFormData
} from '@/types/auth';
import { ApiResponse,ApiError } from '@/types/api';
import { authApiService } from '../services/auth-api';

export function SignupClientForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState<string>('');

  // Use standardized form validation
  const { formState, setValue, handleSubmit, getFieldProps, setError } = useFormValidation<ClientFormData>({
    initialData: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
    },
    validationRules: {
      firstName: createRequiredValidation('First name'),
      lastName: createRequiredValidation('Last name'),
      email: createEmailValidation(),
      phone: createRequiredValidation('Phone number'),
      password: createPasswordValidation(),
    },
    onSubmit: async (data) => {
      setIsSubmitting(true);
      setApiError('');

      try {
        const result = await authApiService.register(data, 'client');

        // Success - redirect to email verification with email parameter
        router.push(`/auth/verify-email?type=client&email=${encodeURIComponent(data.email)}`);

      } catch (error) {
        console.error('Registration error:', error);

        if (error instanceof Error) {
          try {
            const errorResult = JSON.parse(error.message);
            if (errorResult.details && Array.isArray(errorResult.details)) {
              errorResult.details.forEach((detail: any) => {
                const fieldName = detail.field === 'phoneNumber' ? 'phone' : detail.field as keyof ClientFormData;
                setError(fieldName, detail.message);
              });
            } else {
              setApiError(errorResult.message || errorResult.error || 'Registration failed');
            }
          } catch {
            setApiError(error.message || 'Registration failed');
          }
        } else {
          setApiError('Network error. Please try again.');
        }
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  const handleGoogleSignup = () => {
    // Mock Google signup
    console.log('Google signup clicked');
  };

  const handleLoginRedirect = () => {
    router.push('/auth/login');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-5">
      {/* API Error Display */}
      {apiError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
          {apiError}
        </div>
      )}

      {/* First Name */}
      <Input
        type="text"
        name="firstName"
        placeholder="Enter your First Name"
        {...getFieldProps('firstName')}
        onChange={(e) => getFieldProps('firstName').onChange(e.target.value)}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        }
        label="First Name"
      />

      {/* Last Name */}
      <Input
        type="text"
        name="lastName"
        placeholder="Enter your Last Name"
        {...getFieldProps('lastName')}
        onChange={(e) => getFieldProps('lastName').onChange(e.target.value)}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        }
        label="Last Name"
      />

      {/* Email */}
      <Input
        type="email"
        name="email"
        placeholder="Enter your Email"
        {...getFieldProps('email')}
        onChange={(e) => getFieldProps('email').onChange(e.target.value)}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        }
        label="Email"
      />

      {/* Phone */}
      <Input
        type="tel"
        name="phone"
        placeholder="Enter your Phone Number"
        {...getFieldProps('phone')}
        onChange={(e) => getFieldProps('phone').onChange(e.target.value)}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        }
        label="Phone Number"
      />

      {/* Password */}
      <Input
        type={showPassword ? 'text' : 'password'}
        name="password"
        placeholder="• • • • • • • •"
        {...getFieldProps('password')}
        onChange={(e) => getFieldProps('password').onChange(e.target.value)}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        }
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {showPassword ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              )}
            </svg>
          </button>
        }
        label="Password"
      />

      {/* Register Button */}
      <Button
        type="submit"
        className="w-full mt-6"
        size="lg"
        disabled={isSubmitting || !formState.isValid}
      >
        {isSubmitting ? 'Creating Account...' : 'Register'}
      </Button>

      {/* Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 border-t border-gray-300"></div>
        <span className="px-4 text-sm text-gray-500">or Continue with</span>
        <div className="flex-1 border-t border-gray-300"></div>
      </div>

      {/* Google Sign Up */}
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignup}
        className="w-full flex items-center justify-center gap-3"
        size="lg"
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Google
      </Button>

      {/* Login Link */}
      <div className="text-center mt-6">
        <span className="text-gray-600">Already have an account? </span>
        <button
          type="button"
          onClick={handleLoginRedirect}
          className="text-amber-800 hover:text-amber-900 font-medium"
        >
          Login
        </button>
      </div>
    </form>
  );
}
